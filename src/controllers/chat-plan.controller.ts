import {inject} from '@loopback/core';
import {repository, juggler, IsolationLevel} from '@loopback/repository'; // Added IsolationLevel import
import {
	api,
	getModelSchemaRef,
	param,
	patch,
	post,
	requestBody,
	response,
	RestBindings,
	HttpErrors,
} from '@loopback/rest';
import {authenticate, AuthenticationBindings} from '@loopback/authentication';
import {authorize} from '@loopback/authorization';
import {service} from '@loopback/core';
import {SecurityBindings, UserProfile} from '@loopback/security';
import {LLMRouterService} from '../services/chat/llm-router.service';
import {CompletionMessage, RouterParams} from '../services/chat/types';

import {
	PlanCampaignContentRepository, // Added repository
	PlannerCampaignRepository,
	TaskRepository,
	TaskStepRepository,
	// Added new repositories
	OrganizationPlannerPlanRepository,
	PlannerPlanVersionRepository,
	ConversationRepository,
} from '../repositories';
import {TaskService} from '../services/tasks/task.service';
// Added new models
import {PlanCampaignContent, PlannerCampaign, Task, TaskStep, OrganizationPlannerPlan, PlannerPlanVersion} from '../models'; // Added model
import {skipGuardCheck, injectUserOrgId, guardStrategy, GuardSkipStrategy} from '../interceptors';
import {basicAuthorization} from '../services';
import {capitalizeFirstLetter} from '../utils/utils';

interface CampaignCreationData {
	name: string;
	type: string;
	taskType: string;
	description?: string;
	targetSegment?: string;
	scheduledDate?: string;
	businessGoal?: string;
	promotionTitle?: string;
	promotionDescription?: string;
	promotionType?: string;
	plannerPlanVersionId?: number; // Added to support connection to plan version
	chatId?: number; // Added to support linking campaign to conversation
}

interface ChatPlanRequestBody {
chatId: number;
briefText: string;
emailDesign?: string;
emailHtml?: string;
}

interface LlmCampaignOutput {
name: string;
type: 'Email' | 'SMS';
scheduledDate: string;
description: string;
targetSegment: string;
subjectLine?: string;
previewText?: string;
briefText: string;
}
// --- End Existing Interfaces ---

// --- Interfaces for buildPlanFromChat ---
interface BuildPlanCampaign {
  name: string;
  type: string; // 'Email', 'SMS', etc.
  description: string;
  targetSegment: string;
  scheduledDate: string; // ISO 8601 date string
  businessGoal?: string;
  promotionTitle?: string;
  promotionDescription?: string;
  promotionType?: string;
  whyText?: string;
  // Add other relevant PlannerCampaign fields if needed from the model
  subjectLine?: string;
  previewText?: string;
  emailBriefText?: string;
  emailJSON?: string;
  promotionSuggestionReason?: string;
  taskType?: string;
}

// Define BuildPlanRequest as a model for schema generation
import {Model, model, property} from '@loopback/repository';

@model({
  name: 'BuildPlanCampaign', // Using the interface name for the model seems conventional
  description: 'Details of a single campaign within a build plan request.',
})
class BuildPlanCampaignModel extends Model {
  @property({type: 'string', required: true})
  name: string;

  @property({type: 'string', required: true})
  type: string; // 'Email', 'SMS', etc.

  @property({type: 'string', required: true})
  description: string;

  @property({type: 'string', required: true})
  targetSegment: string;

  @property({type: 'string', format: 'date-time', required: true})
  scheduledDate: string; // ISO 8601 date string

  @property({type: 'string'})
  businessGoal?: string;

  @property({type: 'string'})
  promotionTitle?: string;

  @property({type: 'string'})
  promotionDescription?: string;

  @property({type: 'string'})
  promotionType?: string;

  @property({type: 'string'})
  whyText?: string;

  @property({type: 'string'})
  subjectLine?: string;

  @property({type: 'string'})
  previewText?: string;

  @property({type: 'string'})
  emailBriefText?: string;

  @property({type: 'string'}) // Assuming JSON is stored as a string
  emailJSON?: string;

  @property({type: 'string'})
  promotionSuggestionReason?: string;

  @property({type: 'string'})
  taskType?: string;
}

@model({
  name: 'BuildPlanRequest',
  description: 'Request body for building a marketing plan from chat.',
})
class BuildPlanRequestModel extends Model {
  @property({type: 'string', required: true})
  planName: string;

  @property({type: 'string'})
  planDescription?: string;

  @property({type: 'string'})
  planBusinessGoal?: string;

  @property({type: 'string', format: 'date-time'}) // Use date-time for ISO 8601
  planStartDate?: string;

  @property({type: 'string', format: 'date-time'}) // Use date-time for ISO 8601
  planEndDate?: string;

  @property({type: 'string'})
  versionPrompt?: string;

  @property({type: 'string'})
  versionDescription?: string;

  @property({type: 'string'})
  dataSummary?: string;

  @property({type: 'number'})
  chatId?: number; // Add chatId to link campaigns back to conversation

  // Define campaigns using a nested model or a plain object array schema
  // Using a plain object array schema here for simplicity within the controller file
  // Define campaigns using the nested BuildPlanCampaignModel
  @property.array(BuildPlanCampaignModel, { required: true })
  campaigns: BuildPlanCampaign[]; // Use the interface for type checking in code
}


interface BuildPlanResponse {
  success: boolean;
  planId: number;
  planVersionId: number;
  message?: string;
}
// --- End Interfaces for buildPlanFromChat ---


@api({basePath: '/api/v1'})
@guardStrategy(new GuardSkipStrategy())
export class ChatPlanController {
	constructor(
	// Inject DataSource for transactions
	@inject('datasources.dev_db') protected dataSource: juggler.DataSource, // Corrected injection key for the datasource instance
	@repository(PlannerCampaignRepository)
	public plannerCampaignRepository: PlannerCampaignRepository,
	@repository(TaskRepository)
	public taskRepository: TaskRepository,
	@repository(TaskStepRepository)
	public taskStepRepository: TaskStepRepository,
	// Inject new repositories
	@repository(OrganizationPlannerPlanRepository)
	public orgPlanRepo: OrganizationPlannerPlanRepository,
	@repository(PlannerPlanVersionRepository)
	public planVersionRepo: PlannerPlanVersionRepository,
	// Inject PlanCampaignContentRepository
	@repository(PlanCampaignContentRepository)
	public planCampaignContentRepository: PlanCampaignContentRepository,
	@service(TaskService)
	public taskService: TaskService,
	@service(LLMRouterService)
	public llmRouter: LLMRouterService,
	@inject(SecurityBindings.USER) private currentUserProfile: UserProfile,
	@repository(ConversationRepository)
	public conversationRepository: ConversationRepository,
) { }

	private async _createCampaignAndTasks(
		data: CampaignCreationData,
		orgId: number,
	): Promise<{campaign: PlannerCampaign; task: Task}> {
		const newCampaignData: Partial<PlannerCampaign> = {
			...data,
			isTemplate: false,
		};

		// Format the campaign type to capitalize first letter and lowercase the rest
		if (newCampaignData.type) {
			newCampaignData.type = capitalizeFirstLetter(newCampaignData.type);
		}

		if (!newCampaignData.name || !newCampaignData.type) {
			console.warn('Potential missing fields during campaign creation/update via helper.');
		}


		const taskTypeForService = newCampaignData.type;
		if (!taskTypeForService) {
			throw new HttpErrors.BadRequest('Campaign type is required to determine task type for regeneration.');
		}

	// Note: This helper doesn't use transactions, might need adjustment if called elsewhere
	const plannerCampaign = await this.plannerCampaignRepository.create(newCampaignData);

	const task = await this.taskService.createTaskForPlannerCampaign(plannerCampaign);

	return {campaign: plannerCampaign, task};
	}

	@skipGuardCheck()
	@authenticate('jwt')
	@authorize({
		allowedRoles: ['admin', 'support', 'customer'],
		voters: [basicAuthorization],
	})
	@patch('/chat/plan/campaign/{id}')
	public async updateById(
		@param.path.number('id') id: number,
		@requestBody({
			content: {
				'application/json': {
					schema: {
						type: 'object',
						title: 'PlannerCampaignPatch',
						properties: {
							name: {type: 'string'},
							type: {type: 'string', enum: ['Email', 'SMS']},
							description: {type: 'string'},
							targetSegment: {type: 'string'},
							scheduledDate: {type: 'string', format: 'date-time'},
							businessGoal: {type: 'string'},
							promotionTitle: {type: 'string'},
							promotionDescription: {type: 'string'},
							promotionType: {type: 'string'},
							promotionSuggestionReason: {type: 'string'},
							whyText: {type: 'string'},
							emailBriefText: {type: 'string'},
							emailJSON: {type: 'string'},
							subjectLine: {type: 'string'},
							previewText: {type: 'string'},
						},
					},
				},
			},
		})
		campaignUpdateData: Partial<PlannerCampaign>,
		@inject(SecurityBindings.USER) userProfile: UserProfile,
	): Promise<void> {
		const orgId = userProfile.orgId;
		if (!orgId) {
			throw new HttpErrors.Unauthorized('Organization ID not found in user profile.');
		}

	const existingCampaign = await this.plannerCampaignRepository.findOne({
		where: {id: id},
		// TODO: Add orgId check here for security
		});

		if (!existingCampaign) {
			throw new HttpErrors.NotFound(`PlannerCampaign with id ${id} not found for this organization.`);
		}

		const fieldsTriggeringRegeneration: (keyof PlannerCampaign)[] = [
			'type',
			'scheduledDate',
			'businessGoal',
			'promotionType',
			'whyText',
		];

		// Format the campaign type if it's being updated
		if (campaignUpdateData.type) {
			campaignUpdateData.type = capitalizeFirstLetter(campaignUpdateData.type);
		}

		let needsRegeneration = false;
		for (const field of fieldsTriggeringRegeneration) {
			if (
				campaignUpdateData[field] !== undefined &&
				campaignUpdateData[field] !== existingCampaign[field]
			) {
				needsRegeneration = true;
				break;
			}
		}

		if (needsRegeneration) {
			console.log(`Regenerating tasks for campaign ${id} due to changes.`);
			const existingTaskId = (existingCampaign as any).taskId;

			if (existingTaskId) {
			try {
				// Consider transaction here if deleting task/steps needs to be atomic with campaign update
				await this.taskStepRepository.deleteAll({taskId: existingTaskId});
				await this.taskRepository.deleteById(existingTaskId);
				console.log(`Deleted existing task ${existingTaskId} and its steps.`);
			} catch (error) {
				console.error(`Error deleting existing task/steps for campaign ${id} (Task ID: ${existingTaskId}):`, error);
				// Potentially re-throw or handle more gracefully
				}
			} else {
				console.log(`No existing task found for campaign ${id}, skipping deletion.`);
			}

			const campaignDataForTaskService = {...existingCampaign, ...campaignUpdateData};

			const tempCampaignForTaskCreation = new PlannerCampaign(campaignDataForTaskService);
			const newTask = await this.taskService.createTaskForPlannerCampaign(tempCampaignForTaskCreation);

			if (!newTask || !newTask.id) {
				throw new HttpErrors.InternalServerError('Failed to create new tasks.');
			}
			console.log(`Created new task ${newTask.id} and its steps.`);

			const finalUpdateData = {
				...campaignUpdateData,
				taskId: newTask.id,
			};
			await this.plannerCampaignRepository.updateById(id, finalUpdateData);

		} else {
			console.log(`Updating campaign ${id} without task regeneration.`);
			await this.plannerCampaignRepository.updateById(id, campaignUpdateData);
		}

	}

	@skipGuardCheck()
	@authenticate('jwt')
	@authorize({
		allowedRoles: ['admin', 'support', 'customer', 'raleon-admin'],
		voters: [basicAuthorization],
	})
	@post('/chat/plan/campaign')
	@response(200, {
		description: 'Chat Plan Campaign created successfully via AI processing',
		content: {
			'application/json': {
				schema: {
					type: 'object',
					properties: {
						success: {type: 'boolean'},
						campaignId: {type: 'number'},
						taskId: {type: 'number'},
					},
				},
			},
		},
	})
	public async createCampaignFromChat(
		@requestBody({
			description: 'Data needed to create a campaign from chat brief',
			required: true,
			content: {
				'application/json': {
					schema: {
						type: 'object',
						required: ['chatId', 'briefText'],
						properties: {
							chatId: {type: 'number'},
							briefText: {type: 'string'},
							emailDesign: {type: 'string'},
							emailHtml: {type: 'string'},
						},
					},
				},
			},
		})
		requestData: ChatPlanRequestBody,
		@injectUserOrgId() orgId: number
	): Promise<{success: boolean; campaignId: number; taskId: number}> {
		if (!orgId) {
			throw new HttpErrors.Unauthorized('Organization ID not found in user profile.');
		}

		const {chatId, briefText, emailDesign, emailHtml} = requestData;
		const MAX_RETRIES = 3;
		let llmResponseData: LlmCampaignOutput | null = null;

		const prompt = `
      You are an AI assistant tasked with parsing marketing campaign briefs.
      Analyze the following brief text provided by the user and extract the specified fields.
      Return ONLY a valid JSON object containing these fields. Do not include any explanatory text before or after the JSON.

      Brief Text:
      ---
      ${briefText}
      ---

      Extract the following fields:
      - name: (string) A concise name for the campaign.
      - type: (string) The campaign type. Must be either 'Email' or 'SMS'.
      - scheduledDate: (string) The target send date in 'YYYY-MM-DD' ISO 8601 format. If not specified, use today's date (${new Date().toISOString().split('T')[0]}).
      - description: (string) A brief description of the campaign's purpose or content.
      - targetSegment: (string) The intended audience or customer segment. If not specified, use "General Audience".
      - subjectLine: (string, optional for SMS) The subject line for the email.
      - previewText: (string, optional for SMS) The preview text (preheader) for the email.
      - briefText: (string) The original brief text, potentially cleaned up or summarized.

      Example JSON Output Structure:
      {
        "name": "Spring Sale Announcement",
        "type": "Email",
        "scheduledDate": "2024-04-15",
        "description": "Announce the upcoming spring sale event.",
        "targetSegment": "Newsletter Subscribers",
        "subjectLine": "🌸 Spring Sale Starts Soon!",
        "previewText": "Get ready for amazing deals.",
        "briefText": "Subject: Spring Sale! Preview: Deals coming. Segment: Newsletter Subs. Send on April 15th. Announce the sale."
      }

      Important:
      - Ensure the 'type' is strictly 'Email' or 'SMS'.
      - Ensure 'scheduledDate' is in 'YYYY-MM-DD' format.
      - The final output MUST be only the JSON object.
    `;

		for (let attempt = 1; attempt <= MAX_RETRIES; attempt++) {
			try {
				console.log(`Attempt ${attempt} to process campaign brief with LLM...`);
				const messages: CompletionMessage[] = [
					{role: 'system', content: prompt},
					{role: 'user', content: `Parse this brief: ${briefText}`}
				];
				const params: RouterParams = {
					models: ['openai/o3-mini', 'anthropic/claude-3.5-sonnet'],
					maxTokens: 500,
					temperature: 0.2,
				};
				const llmResult = await this.llmRouter.executeCompletion(messages, params);

				if (!llmResult || !llmResult.content) {
					throw new Error('LLM returned empty response.');
				}

				const jsonStart = llmResult.content.indexOf('{');
				const jsonEnd = llmResult.content.lastIndexOf('}');
				if (jsonStart === -1 || jsonEnd === -1 || jsonEnd < jsonStart) {
					throw new Error('Valid JSON object not found in LLM response.');
				}
				const jsonString = llmResult.content.substring(jsonStart, jsonEnd + 1);
				const parsedJson = JSON.parse(jsonString);


				if (
					!parsedJson.name ||
					!parsedJson.type ||
					!parsedJson.scheduledDate ||
					!parsedJson.description ||
					!parsedJson.targetSegment ||
					(parsedJson.type === 'Email' && (!parsedJson.subjectLine || !parsedJson.previewText))
				) {
					throw new Error('LLM response missing required fields or invalid structure.');
				}

				if (typeof parsedJson.name !== 'string' ||
					(parsedJson.type !== 'Email' && parsedJson.type !== 'SMS') ||
					typeof parsedJson.scheduledDate !== 'string' ||
					typeof parsedJson.description !== 'string' ||
					typeof parsedJson.targetSegment !== 'string' ||
					(parsedJson.subjectLine && typeof parsedJson.subjectLine !== 'string') ||
					(parsedJson.previewText && typeof parsedJson.previewText !== 'string') ||
					typeof parsedJson.briefText !== 'string') {
					throw new Error('LLM response contains fields with incorrect types.');
				}


				llmResponseData = parsedJson as LlmCampaignOutput;
				console.log('LLM processing successful.');
				break;

			} catch (error: any) {
				console.error(`Attempt ${attempt} failed: ${error.message}`);
				if (attempt === MAX_RETRIES) {
					throw new HttpErrors.InternalServerError(
						`Failed to process campaign details with AI after ${MAX_RETRIES} attempts. Last error: ${error.message}`
					);
				}
				await new Promise(resolve => setTimeout(resolve, 1000 * attempt));
			}
		}

		if (!llmResponseData) {
			throw new HttpErrors.InternalServerError('Failed to obtain valid campaign details from AI.');
		}

		const campaignDataForHelper: CampaignCreationData = {
			name: llmResponseData.name,
			type: llmResponseData.type,
			taskType: llmResponseData.type,
			description: llmResponseData.description,
			targetSegment: llmResponseData.targetSegment,
			scheduledDate: llmResponseData.scheduledDate,
		};


		try {
			// 1. Create a hidden OrganizationPlannerPlan for this campaign
			const newPlan = await this.orgPlanRepo.create({
				organizationId: orgId,
				name: llmResponseData.name,
				description: llmResponseData.description,
				createdDate: new Date(),
				active: false,
				archived: false,
				inProgress: false,
				hiddenToUsers: true, // Set hidden flag to true as requested
				generationStatus: 'complete',
				generationProgress: 100,
			});

			if (!newPlan?.id) {
				throw new Error('Failed to create OrganizationPlannerPlan for campaign.');
			}

			// 2. Create a PlannerPlanVersion for this plan
			const newVersion = await this.planVersionRepo.create({
				organizationPlannerPlanId: newPlan.id,
				description: `Auto-generated version for ${llmResponseData.name}`,
				active: true,
			});

			if (!newVersion?.id) {
				throw new Error('Failed to create PlannerPlanVersion for campaign.');
			}

			// 3. Add version ID and chatId to campaign data
			campaignDataForHelper.plannerPlanVersionId = newVersion.id;
			campaignDataForHelper.chatId = chatId; // Add chatId to the campaign data



			// 4. Create campaign and tasks as before
			const {campaign: createdCampaign, task: createdTask} = await this._createCampaignAndTasks(
				campaignDataForHelper, // Interface updated to support plannerPlanVersionId
				orgId,
			);

			if (!createdCampaign?.id || !createdTask?.id) {
				throw new Error('Helper function did not return valid campaign or task IDs.');
			}

			// Update campaign with chatId and productUrlsChatId
			const campaignUpdateData: Partial<PlannerCampaign> = {
				chatId: chatId,
				productUrlsChatId: chatId, // Set the dedicated field for product URLs
			};

			// Update task with emailDesign, emailHtml, and conversationId
			const taskUpdateData: Partial<Task> = {
				conversationId: chatId, // Keep existing functionality
				productUrlsConversationId: chatId // Set the dedicated field for product URLs
			};

			if (emailDesign) {
				taskUpdateData.emailDesign = JSON.parse(emailDesign);
			}

			if (emailHtml) {
				taskUpdateData.emailHtml = emailHtml;
			}

			// Apply updates to their respective repositories
			if (Object.keys(campaignUpdateData).length > 0) {
				await this.plannerCampaignRepository.updateById(createdCampaign.id, campaignUpdateData);
				console.log(`Updated campaign ${createdCampaign.id} with chatId.`);
			}

			if (Object.keys(taskUpdateData).length > 0) {
				await this.taskRepository.updateById(createdTask.id, taskUpdateData);
				console.log(`Updated task ${createdTask.id} with conversationId and email data.`);
			}
			// Also update the conversation to set taskId for reverse relationship
			await this.conversationRepository.updateById(chatId, {
				taskId: createdTask.id,
			});
			console.log(`Updated conversation ${chatId} with taskId and campaignId.`);

			//Update one of the task steps with the rawBriefContent
			await this.taskRepository.taskSteps(createdTask.id).patch({
				data: briefText
			}, {
				taskTypeId: 3 // Assuming 3 is the ID for the 'Content' task type
			});


			return {
				success: true,
				campaignId: createdCampaign.id,
				taskId: createdTask.id,
			};
		} catch (error: any) {
		console.error('Error during campaign/task creation or update:', error);
		if (error instanceof HttpErrors.HttpError) {
			throw error;
		}
			throw new HttpErrors.InternalServerError(`Failed to process campaign creation: ${error.message}`);
		}
	}

// --- End Existing createCampaignFromChat method ---


// --- NEW buildPlanFromChat Method ---
@post('/chat/plan/build')
@response(200, {
	description: 'Plan built successfully from chat data',
	content: {
		'application/json': {
			schema: {
				type: 'object',
				title: 'BuildPlanSuccessResponse',
				properties: {
					success: {type: 'boolean'},
					planId: {type: 'number'},
					planVersionId: {type: 'number'}, // Corrected field name
					message: {type: 'string'},
				},
			},
		},
	},
})
@authenticate('jwt')
@authorize({
	allowedRoles: ['admin', 'support', 'customer'], // Updated roles as per spec
	voters: [basicAuthorization],
})
@skipGuardCheck()
public async buildPlanFromChat(
	@requestBody({
		description: 'Data for creating a plan, version, and campaigns from chat',
		required: true,
		content: {
			'application/json': {
				// Use getModelSchemaRef with the defined model
				schema: getModelSchemaRef(BuildPlanRequestModel),
			},
		},
	})
	requestBody: BuildPlanRequestModel, // Use the Model for validation, but can use the Interface for internal logic
	@injectUserOrgId() orgId: number,
): Promise<BuildPlanResponse> { // Use the defined response interface
	if (!orgId) {
		throw new HttpErrors.Unauthorized('Organization ID not found in user profile.');
	}



	// Validate request body (LoopBack handles schema validation based on @requestBody)
	if (!requestBody || !requestBody.planName || !requestBody.campaigns || requestBody.campaigns.length === 0) {
		throw new HttpErrors.BadRequest('Invalid request: Missing plan name or campaigns.');
	}
	// Add more specific validation if needed (e.g., date formats, campaign fields)

	const tx = await this.dataSource.beginTransaction({
		isolationLevel: IsolationLevel.READ_COMMITTED, // Use imported IsolationLevel directly
	});

	try {
		// 1. Create OrganizationPlannerPlan
		const newPlan = await this.orgPlanRepo.create({
			organizationId: orgId,
			name: requestBody.planName,
			description: requestBody.planDescription,
			businessGoal: requestBody.planBusinessGoal,
			startdate: requestBody.planStartDate,
			enddate: requestBody.planEndDate,
			createdDate: new Date(),
			active: false,
			archived: false,
			inProgress: false,
			generationStatus: 'complete',
			generationProgress: 100,
		}, {transaction: tx});

		if (!newPlan?.id) {
			throw new Error('Failed to create OrganizationPlannerPlan.');
		}

		// 2. Create PlannerPlanVersion
		const newVersion = await this.planVersionRepo.create({
			organizationPlannerPlanId: newPlan.id,
			prompt: requestBody.versionPrompt,
			description: requestBody.versionDescription,
			active: true,
			dataSummary: requestBody.dataSummary,
		}, {transaction: tx});

		if (!newVersion?.id) {
			throw new Error('Failed to create PlannerPlanVersion.');
		}

		// 3. Create PlannerCampaigns
		for (const campaignData of requestBody.campaigns) {
			// Basic validation for each campaign (LoopBack schema validation helps)
			if (!campaignData.name || !campaignData.type || !campaignData.description || !campaignData.targetSegment || !campaignData.scheduledDate) {
				// This check might be redundant if schema validation is strict enough
				console.warn('Skipping campaign due to missing required fields (should be caught by schema validation):', campaignData.name);
				continue;
			}

			// Map BuildPlanCampaign to PlannerCampaign properties
			const newCampaign: Partial<PlannerCampaign> = {
				plannerPlanVersionId: newVersion.id,
				name: campaignData.name,
				type: capitalizeFirstLetter(campaignData.type), // Format the type with first letter capitalized
				description: campaignData.description,
				targetSegment: campaignData.targetSegment,
				scheduledDate: campaignData.scheduledDate, // Use string directly from request data
				businessGoal: campaignData.businessGoal,
				promotionTitle: campaignData.promotionTitle,
				promotionDescription: campaignData.promotionDescription,
				promotionType: campaignData.promotionType,
				whyText: campaignData.whyText,
				promotionSuggestionReason: campaignData.promotionSuggestionReason,
				taskType: 'Email',
				isTemplate: false,
				chatId: requestBody.chatId, // Link campaign to original chat conversation
				productUrlsChatId: requestBody.chatId, // Set the dedicated field for product URLs
			};

			// Create the PlannerCampaign first to get its ID
			const createdCampaign = await this.plannerCampaignRepository.create(newCampaign, {transaction: tx});

			if (!createdCampaign?.id) {
				throw new Error(`Failed to create PlannerCampaign for: ${campaignData.name}`);
			}



			// Use TaskService to create the task and associated steps/content
			// The service handles finding the correct template based on campaign type
			const createdTask = await this.taskService.createTaskForPlannerCampaign(createdCampaign); // Note: TaskService currently doesn't accept transaction

			if (!createdTask?.id) {
				throw new Error(`Failed to create Task for campaign: ${createdCampaign.name}`);
			}

			// Set conversationId and productUrlsConversationId on the task to link it back to the original chat
			if (requestBody.chatId) {
				await this.taskRepository.updateById(createdTask.id, {
					conversationId: requestBody.chatId,
					productUrlsConversationId: requestBody.chatId // Set the dedicated field for product URLs
				});
			}

			// The TaskService sets the plannerCampaignId on the Task, so no update needed here.
		}

		// 4. Commit Transaction
		await tx.commit();

		// 5. Update conversation to link back to the plan (if chatId provided)
		if (requestBody.chatId) {
			try {
				await this.conversationRepository.updateById(requestBody.chatId, {
					// For plans with multiple campaigns, we'll just link to the plan itself
					// Individual campaigns already have chatId set
				});
			} catch (error) {
				console.error('Error updating conversation with plan info:', error);
				// Don't fail the entire operation if conversation update fails
			}
		}

		// 6. Return Success Response
		return {
			success: true,
			planId: newPlan.id,
			planVersionId: newVersion.id, // Corrected field name
			message: 'Marketing plan built successfully.',
		};

	} catch (error) {
		// 6. Rollback Transaction on error
		await tx.rollback();
		console.error('Error building plan from chat:', error);
		if (error instanceof HttpErrors.HttpError) {
			throw error; // Re-throw known HTTP errors
		}
		// Throw a generic server error for unknown issues
		throw new HttpErrors.InternalServerError(`Failed to build plan: ${error.message || error}`);
	}
}
// --- End NEW buildPlanFromChat Method ---

} // End of class
