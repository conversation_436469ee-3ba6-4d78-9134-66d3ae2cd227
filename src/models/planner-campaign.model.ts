import {Entity, model, property, hasOne, hasMany} from '@loopback/repository';
import {Task} from './task.model';
import {PlanCampaignContent} from './plan-campaign-content.model';
import {PlannerCampaignImage} from './planner-campaign-image.model';

@model()
export class PlannerCampaign extends Entity {
  @property({
    type: 'number',
    id: true,
    generated: true,
  })
  id?: number;

  @property({
    type: 'string',
    required: true,
  })
  name: string;

  @property({
    type: 'string',
    required: true,
  })
  type: string;

  @property({
    type: 'string',
    required: true,
  })
  description: string;

  @property({
    type: 'string',
    required: true,
  })
  targetSegment: string;

  @property({
    type: 'date',
    required: true,
  })
  scheduledDate: string;

  @property({
    type: 'string',
  })
  businessGoal?: string;

  @property({
    type: 'string',
  })
  promotionTitle?: string;

  @property({
    type: 'string',
  })
  promotionDescription?: string;

  @property({
    type: 'string',
  })
  promotionType?: string;

  @property({
    type: 'string',
  })
  promotionSuggestionReason?: string;

  @property({
    type: 'number',
  })
  plannerPlanVersionId?: number;

  @property({
	type: 'string'
  })
  taskType?: string;

  @property({
	type: 'string'
  })
  whyText?: string;

  @property({
	type: 'boolean',
  })
  isTemplate?: boolean;

  @property({
    type: 'number',
    required: false,
  })
  chatId?: number;

  @property({
    type: 'number',
    required: false,
    jsonSchema: {
      title: 'Original Chat ID',
      description: 'The ID of the original conversation containing product URLs for this campaign'
    }
  })
  originalChatId?: number;

  @property({
    type: 'boolean',
    default: false,
  })
  isDeleted?: boolean;

  @hasOne(() => Task)
  task: Task;

  @hasOne(() => PlanCampaignContent)
  planCampaignContent: PlanCampaignContent;

  @hasMany(() => PlannerCampaignImage)
  plannerCampaignImages: PlannerCampaignImage[];

  constructor(data?: Partial<PlannerCampaign>) {
    super(data);
  }
}

export interface PlannerCampaignRelations {
  // describe navigational properties here
}

export type PlannerCampaignWithRelations = PlannerCampaign & PlannerCampaignRelations;
